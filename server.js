const express = require('express');
const multer = require('multer');
const XLSX = require('xlsx');
const path = require('path');
const fs = require('fs');
const os = require('os');

// 创建 Express 应用
const app = express();
const port = 3000;

// 配置中间件 - 增加请求体大小限制
app.use(express.json({ limit: '100mb' }));
app.use(express.urlencoded({ extended: true, limit: '100mb' }));

// 添加超时设置
app.use((req, res, next) => {
  // 设置较长的超时时间，避免大文件处理时请求超时
  req.setTimeout(300000); // 5分钟
  res.setTimeout(300000); // 5分钟
  next();
});

// 静态文件服务
app.use(express.static(path.join(__dirname, 'public')));

// 确保上传目录存在
const uploadsDir = path.join(__dirname, 'uploads');
if (!fs.existsSync(uploadsDir)) {
  fs.mkdirSync(uploadsDir);
}

// 配置文件上传
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, uploadsDir);
  },
  filename: function (req, file, cb) {
    cb(null, Date.now() + '-' + file.originalname);
  }
});

// 设置 multer 配置，增加文件大小限制
const upload = multer({ 
  storage: storage,
  limits: {
    fileSize: 100 * 1024 * 1024 // 增加到100MB 限制
  }
});

// 清理电话号码函数
function cleanPhoneNumber(phone, removeCountryCode = false) {
  if (!phone) return '';
  
  // 转换为字符串
  let phoneStr = String(phone);
  
  // 移除所有非数字字符
  phoneStr = phoneStr.replace(/\D/g, '');
  
  // 如果需要移除国家代码（假设是+86或86开头）
  if (removeCountryCode && phoneStr.length > 10) {
    if (phoneStr.startsWith('86') && phoneStr.length > 11) {
      phoneStr = phoneStr.substring(2);
    } else if (phoneStr.length > 11) {
      // 如果长度大于11，假设前面的是国家代码
      phoneStr = phoneStr.substring(phoneStr.length - 11);
    }
  }
  
  return phoneStr;
}

// 日期解析函数
function parseDate(dateStr) {
  if (!dateStr) return null;
  
  // 转换为字符串以确保一致性
  const str = String(dateStr).trim();
  
  // 尝试直接解析
  let date = new Date(str);
  if (!isNaN(date.getTime())) {
    return date;
  }
  
  // 处理YYYY/MM/DD或YYYY-MM-DD格式
  let match = str.match(/^(\d{4})[\/\-\.](\d{1,2})[\/\-\.](\d{1,2})$/);
  if (match) {
    date = new Date(parseInt(match[1]), parseInt(match[2]) - 1, parseInt(match[3]));
    return date;
  }
  
  // 处理DD/MM/YYYY或DD-MM-YYYY格式
  match = str.match(/^(\d{1,2})[\/\-\.](\d{1,2})[\/\-\.](\d{4})$/);
  if (match) {
    date = new Date(parseInt(match[3]), parseInt(match[2]) - 1, parseInt(match[1]));
    return date;
  }
  
  // 处理YYYY年MM月DD日格式
  match = str.match(/^(\d{4})年(\d{1,2})月(\d{1,2})日?$/);
  if (match) {
    date = new Date(parseInt(match[1]), parseInt(match[2]) - 1, parseInt(match[3]));
    return date;
  }
  
  // 处理YYYYMMDD格式
  match = str.match(/^(\d{4})(\d{2})(\d{2})$/);
  if (match) {
    date = new Date(parseInt(match[1]), parseInt(match[2]) - 1, parseInt(match[3]));
    return date;
  }
  
  return null;
}

// 获取本机 IP 地址
function getLocalIP() {
  const nets = os.networkInterfaces();
  for (const name of Object.keys(nets)) {
    for (const net of nets[name]) {
      if (net.family === 'IPv4' && !net.internal) {
        return net.address;
      }
    }
  }
  return '127.0.0.1';
}
// API 路由: 获取 Excel 文件的工作表列表
app.post('/api/get-sheets', upload.single('file'), (req, res) => {
  console.log('收到获取工作表请求');
  try {
    if (!req.file) {
      console.log('未上传文件');
      return res.status(400).json({ error: '未上传文件' });
    }
    
    console.log('文件路径:', req.file.path);
    const filePath = req.file.path;
    
    // 检查文件是否存在
    if (!fs.existsSync(filePath)) {
      console.log('文件不存在:', filePath);
      return res.status(400).json({ error: '文件不存在' });
    }
    
    // 检查文件大小
    const stats = fs.statSync(filePath);
    console.log('文件大小:', stats.size, '字节');
    
    // 读取Excel文件 - 优化读取选项
    const workbook = XLSX.readFile(filePath, {
      cellFormula: false,
      cellStyles: false,
      cellDates: true,
      dense: true,
      sheetStubs: true,
      type: 'binary',
      rawHTML: false,
      cellHTML: false,
      sheetRows: 1 // 只读取第一行以获取表头，提高性能
    });
    
    const sheetNames = workbook.SheetNames;
    console.log('工作表名称:', sheetNames);
    
    const result = {
      filePath: filePath,
      sheetNames: sheetNames,
      sheets: {}
    };
    
    // 获取每个工作表的列头信息
    sheetNames.forEach(sheetName => {
      const worksheet = workbook.Sheets[sheetName];
      const jsonData = XLSX.utils.sheet_to_json(worksheet, { 
        header: 1, 
        defval: '',
        range: 0,
        blankrows: false,
        sheetRows: 1 // 只读取第一行以获取表头
      });
      
      if (jsonData.length > 0) {
        result.sheets[sheetName] = {
          headers: jsonData[0]
        };
      } else {
        result.sheets[sheetName] = {
          headers: []
        };
      }
    });
    
    console.log('成功获取工作表信息');
    res.json(result);
  } catch (error) {
    console.error('处理Excel文件出错:', error);
    res.status(500).json({ error: error.message });
  }
});

// API 路由: 电话号码去重
app.post('/api/dedup-phones', (req, res) => {
  console.log('收到去重请求:', req.body);
  try {
    const { filePath, sheetName, phoneColumnIndex, removeCountryCode, keepOriginalFormat, dateFilter } = req.body;
    
    if (!filePath || !sheetName) {
      return res.status(400).json({ error: '缺少必要参数' });
    }
    
    // 检查文件是否存在
    if (!fs.existsSync(filePath)) {
      return res.status(400).json({ error: '文件不存在' });
    }
    
    // 读取Excel文件 - 使用优化的读取选项
    const options = {
      cellFormula: false,
      cellStyles: false,
      cellDates: true,
      dense: true,
      sheetStubs: true,
      type: 'binary',
      rawHTML: false,
      cellHTML: false
    };
    
    const workbook = XLSX.readFile(filePath, options);
    
    if (!workbook.SheetNames.includes(sheetName)) {
      return res.status(400).json({ error: '工作表不存在' });
    }
    
    // 获取工作表
    const worksheet = workbook.Sheets[sheetName];
    
    // 获取表头
    const headers = XLSX.utils.sheet_to_json(worksheet, { 
      header: 1, 
      defval: '',
      blankrows: false,
      range: { s: {r: 0, c: 0}, e: {r: 0, c: 1000} } // 只读取第一行
    })[0] || [];
    
    // 分批处理数据
    const BATCH_SIZE = 5000; // 每批处理的行数
    const uniquePhones = new Map();
    const duplicates = new Map();
    let totalProcessed = 0;
    
    // 获取工作表范围
    const range = XLSX.utils.decode_range(worksheet['!ref'] || 'A1');
    const totalRows = range.e.r;
    
    // 分批处理数据
    for (let startRow = 1; startRow <= totalRows; startRow += BATCH_SIZE) {
      const endRow = Math.min(startRow + BATCH_SIZE - 1, totalRows);
      
      // 读取当前批次的数据
      const batchRange = {
        s: { r: startRow, c: 0 },
        e: { r: endRow, c: range.e.c }
      };
      
      const jsonData = XLSX.utils.sheet_to_json(worksheet, { 
        header: 1, 
        defval: '',
        blankrows: false,
        range: batchRange
      });
      
      // 应用日期筛选
      let filteredData = jsonData;
      if (dateFilter && dateFilter.enabled && dateFilter.columnIndex >= 0) {
        filteredData = jsonData.filter(row => {
          if (dateFilter.columnIndex >= row.length || !row[dateFilter.columnIndex]) {
            return false;
          }
          
          const date = parseDate(row[dateFilter.columnIndex]);
          if (!date) return false;
          
          const year = date.getFullYear();
          const month = date.getMonth() + 1;
          
          let match = true;
          if (dateFilter.year && dateFilter.year !== '') {
            const filterYear = parseInt(dateFilter.year);
            if (year !== filterYear) {
              match = false;
            }
          }
          
          if (match && dateFilter.month && dateFilter.month !== '') {
            const filterMonth = parseInt(dateFilter.month);
            if (month !== filterMonth) {
              match = false;
            }
          }
          
          return match;
        });
      }
      
      // 处理当前批次的数据
      filteredData.forEach((row, index) => {
        if (phoneColumnIndex >= row.length || !row[phoneColumnIndex]) return;
        
        const originalPhone = String(row[phoneColumnIndex]);
        const cleanedPhone = cleanPhoneNumber(originalPhone, removeCountryCode);
        
        if (!cleanedPhone) return;
        
        if (uniquePhones.has(cleanedPhone)) {
          // 记录重复项
          if (!duplicates.has(cleanedPhone)) {
            duplicates.set(cleanedPhone, [uniquePhones.get(cleanedPhone)]);
          }
          duplicates.get(cleanedPhone).push(startRow + index);
        } else {
          // 记录唯一项
          uniquePhones.set(cleanedPhone, startRow + index);
        }
      });
      
      totalProcessed += filteredData.length;
    }
    
    // 构建结果
    const uniqueRows = [];
    const duplicateRows = [];
    
    // 再次分批读取数据，获取完整行
    for (let startRow = 1; startRow <= totalRows; startRow += BATCH_SIZE) {
      const endRow = Math.min(startRow + BATCH_SIZE - 1, totalRows);
      
      // 读取当前批次的数据
      const batchRange = {
        s: { r: startRow, c: 0 },
        e: { r: endRow, c: range.e.c }
      };
      
      const jsonData = XLSX.utils.sheet_to_json(worksheet, { 
        header: 1, 
        defval: '',
        blankrows: false,
        range: batchRange
      });
      
      // 处理当前批次的数据
      jsonData.forEach((row, index) => {
        const rowIndex = startRow + index;
        
        // 检查是否是唯一行
        for (const [phone, uniqueIndex] of uniquePhones.entries()) {
          if (uniqueIndex === rowIndex) {
            const newRow = [...row];
            
            // 如果需要保持原始格式，不修改电话号码
            if (!keepOriginalFormat) {
              newRow[phoneColumnIndex] = phone;
            }
            
            uniqueRows.push(newRow);
            break;
          }
        }
        
        // 检查是否是重复行
        for (const [phone, indices] of duplicates.entries()) {
          if (indices.includes(rowIndex)) {
            duplicateRows.push(row);
            break;
          }
        }
      });
      
      // 限制结果数量，避免内存溢出
      if (uniqueRows.length > 50000 || duplicateRows.length > 50000) {
        console.log('结果数量过多，提前结束处理');
        break;
      }
    }
    
    console.log(`去重完成: 唯一号码 ${uniqueRows.length} 个, 重复号码 ${duplicateRows.length} 个`);
    res.json({
      headers: headers,
      uniqueRows: uniqueRows,
      duplicateRows: duplicateRows,
      totalProcessed: totalProcessed
    });
  } catch (error) {
    console.error('去重处理出错:', error);
    res.status(500).json({ error: error.message });
  }
});

// API 路由: 关键词搜索
app.post('/api/search-excel', (req, res) => {
  console.log('收到搜索请求:', req.body);
  try {
    const { filePath, sheetName, searchColumnIndex, keyword, caseSensitive, exactMatch, deduplicateResults, phoneColumnIndex } = req.body;
    
    if (!filePath || !sheetName) {
      return res.status(400).json({ error: '缺少必要参数' });
    }
    
    // 读取Excel文件 - 优化读取选项
    const workbook = XLSX.readFile(filePath, {
      cellFormula: false,
      cellStyles: false,
      cellDates: true,
      dense: true,
      sheetStubs: true,
      type: 'binary',
      rawHTML: false,
      cellHTML: false
    });
    
    if (!workbook.SheetNames.includes(sheetName)) {
      return res.status(400).json({ error: '工作表不存在' });
    }
    
    // 获取工作表
    const worksheet = workbook.Sheets[sheetName];
    
    // 获取表头
    const headers = XLSX.utils.sheet_to_json(worksheet, { 
      header: 1, 
      defval: '',
      blankrows: false,
      range: { s: {r: 0, c: 0}, e: {r: 0, c: 1000} } // 只读取第一行
    })[0] || [];
    
    // 准备搜索关键词
    let searchKeyword = keyword;
    if (!caseSensitive) {
      searchKeyword = searchKeyword.toLowerCase();
    }
    
    // 分批处理数据
    const BATCH_SIZE = 5000; // 每批处理的行数
    const matchedRows = [];
    const uniquePhones = new Set();
    
    // 获取工作表范围
    const range = XLSX.utils.decode_range(worksheet['!ref'] || 'A1');
    const totalRows = range.e.r;
    
    // 分批处理数据
    for (let startRow = 1; startRow <= totalRows; startRow += BATCH_SIZE) {
      const endRow = Math.min(startRow + BATCH_SIZE - 1, totalRows);
      
      // 读取当前批次的数据
      const batchRange = {
        s: { r: startRow, c: 0 },
        e: { r: endRow, c: range.e.c }
      };
      
      const jsonData = XLSX.utils.sheet_to_json(worksheet, { 
        header: 1, 
        defval: '',
        blankrows: false,
        range: batchRange
      });
      
      // 搜索匹配的行
      jsonData.forEach(row => {
        if (searchColumnIndex >= row.length || !row[searchColumnIndex]) return;
        
        let cellValue = String(row[searchColumnIndex]);
        if (!caseSensitive) {
          cellValue = cellValue.toLowerCase();
        }
        
        let isMatch = false;
        if (exactMatch) {
          isMatch = cellValue === searchKeyword;
        } else {
          isMatch = cellValue.includes(searchKeyword);
        }
        
        if (isMatch) {
          // 如果需要去重
          if (deduplicateResults && phoneColumnIndex !== undefined && phoneColumnIndex >= 0) {
            if (phoneColumnIndex >= row.length || !row[phoneColumnIndex]) {
              matchedRows.push(row);
              return;
            }
            
            const phone = cleanPhoneNumber(row[phoneColumnIndex], false);
            if (!phone) {
              matchedRows.push(row);
              return;
            }
            
            if (uniquePhones.has(phone)) {
              return;
            }
            
            uniquePhones.add(phone);
          }
          
          matchedRows.push(row);
          
          // 限制结果数量，避免内存溢出
          if (matchedRows.length >= 10000) {
            return;
          }
        }
      });
      
      // 如果已经找到足够多的结果，提前结束
      if (matchedRows.length >= 10000) {
        console.log('搜索结果超过10000条，提前结束搜索');
        break;
      }
    }
    
    console.log(`搜索完成: 找到 ${matchedRows.length} 条匹配记录`);
    res.json({
      headers: headers,
      matchedRows: matchedRows
    });
  } catch (error) {
    console.error('搜索出错:', error);
    res.status(500).json({ error: error.message });
  }
});

// API 路由: 多电话号码分离
app.post('/api/split-phones', (req, res) => {
  console.log('收到分离请求:', req.body);
  try {
    const { filePath, sheetName, phoneColumnIndex, nameColumnIndex, removeCountryCode, deduplicateResults, dateFilter } = req.body;
    
    if (!filePath || !sheetName) {
      return res.status(400).json({ error: '缺少必要参数' });
    }
    
    // 检查文件是否存在
    if (!fs.existsSync(filePath)) {
      console.log('文件不存在:', filePath);
      return res.status(400).json({ error: '文件不存在' });
    }
    
    // 读取Excel文件 - 优化读取选项
    const workbook = XLSX.readFile(filePath, {
      cellFormula: false,
      cellStyles: false,
      cellDates: true,
      dense: true,
      sheetStubs: true,
      type: 'binary',
      rawHTML: false,
      cellHTML: false
    });
    
    if (!workbook.SheetNames.includes(sheetName)) {
      return res.status(400).json({ error: '工作表不存在' });
    }
    
    // 获取工作表
    const worksheet = workbook.Sheets[sheetName];
    
    // 获取表头
    const headers = XLSX.utils.sheet_to_json(worksheet, { 
      header: 1, 
      defval: '',
      blankrows: false,
      range: { s: {r: 0, c: 0}, e: {r: 0, c: 1000} } // 只读取第一行
    })[0] || [];
    
    // 分批处理数据
    const BATCH_SIZE = 5000; // 每批处理的行数
    const splitRows = [];
    const uniquePhones = new Set();
    let totalProcessed = 0;
    
    // 获取工作表范围
    const range = XLSX.utils.decode_range(worksheet['!ref'] || 'A1');
    const totalRows = range.e.r;
    
    console.log(`处理 ${totalRows} 行数据进行电话号码分离`);
    
    // 分批处理数据
    for (let startRow = 1; startRow <= totalRows; startRow += BATCH_SIZE) {
      const endRow = Math.min(startRow + BATCH_SIZE - 1, totalRows);
      
      // 读取当前批次的数据
      const batchRange = {
        s: { r: startRow, c: 0 },
        e: { r: endRow, c: range.e.c }
      };
      
      const jsonData = XLSX.utils.sheet_to_json(worksheet, { 
        header: 1, 
        defval: '',
        blankrows: false,
        range: batchRange
      });
      
      // 应用日期筛选
      let filteredData = jsonData;
      if (dateFilter && dateFilter.enabled && dateFilter.columnIndex >= 0) {
        filteredData = jsonData.filter(row => {
          if (dateFilter.columnIndex >= row.length || !row[dateFilter.columnIndex]) {
            return false;
          }
          
          const date = parseDate(row[dateFilter.columnIndex]);
          if (!date) return false;
          
          const year = date.getFullYear();
          const month = date.getMonth() + 1;
          
          let match = true;
          if (dateFilter.year && dateFilter.year !== '') {
            const filterYear = parseInt(dateFilter.year);
            if (year !== filterYear) {
              match = false;
            }
          }
          
          if (match && dateFilter.month && dateFilter.month !== '') {
            const filterMonth = parseInt(dateFilter.month);
            if (month !== filterMonth) {
              match = false;
            }
          }
          
          return match;
        });
      }
      
      totalProcessed += filteredData.length;
      
      // 分离电话号码
      filteredData.forEach(row => {
        if (phoneColumnIndex >= row.length || !row[phoneColumnIndex]) return;
        
        const phoneStr = String(row[phoneColumnIndex]);
        
        // 使用多种分隔符分割电话号码
        const phones = phoneStr.split(/[,;，；\s\/\|\n\r]+/);
        
        // 处理可能连在一起的多个电话号码
        let processedPhones = [];
        phones.forEach(phone => {
          // 如果一个字段包含多个11位数字，可能是多个电话号码连在一起
          if (phone.length > 11) {
            // 尝试按照11位一组分割
            const cleanedLongPhone = phone.replace(/\D/g, '');
            if (cleanedLongPhone.length >= 11) {
              // 每11位分割一次
              for (let i = 0; i < cleanedLongPhone.length; i += 11) {
                if (i + 11 <= cleanedLongPhone.length) {
                  const singlePhone = cleanedLongPhone.substring(i, i + 11);
                  processedPhones.push(singlePhone);
                } else if (cleanedLongPhone.length - i >= 8) {
                  // 如果剩余数字大于等于8位，也认为是一个有效电话号码
                  const singlePhone = cleanedLongPhone.substring(i);
                  processedPhones.push(singlePhone);
                }
              }
            } else {
              processedPhones.push(phone);
            }
          } else {
            processedPhones.push(phone);
          }
        });
        
        processedPhones.forEach(phone => {
          if (!phone) return;
          
          const cleanedPhone = cleanPhoneNumber(phone, removeCountryCode);
          if (!cleanedPhone || cleanedPhone.length < 8) {
            return; // 跳过无效电话号码
          }
          
          // 如果需要去重并且电话号码已存在，则跳过
          if (deduplicateResults && uniquePhones.has(cleanedPhone)) {
            return;
          }
          
          // 创建新行
          const newRow = [...row];
          newRow[phoneColumnIndex] = cleanedPhone;
          
          splitRows.push(newRow);
          
          if (deduplicateResults) {
            uniquePhones.add(cleanedPhone);
          }
          
          // 限制结果数量，避免内存溢出
          if (splitRows.length >= 100000) {
            return;
          }
        });
        
        // 如果已经找到足够多的结果，提前结束
        if (splitRows.length >= 100000) {
          return;
        }
      });
      
      // 如果已经找到足够多的结果，提前结束
      if (splitRows.length >= 100000) {
        console.log('分离结果超过100000条，提前结束处理');
        break;
      }
    }
    
    console.log(`分离完成: 共生成 ${splitRows.length} 行数据`);
    res.json({
      headers: headers,
      splitRows: splitRows,
      totalProcessed: totalProcessed
    });
  } catch (error) {
    console.error('分离电话号码出错:', error);
    res.status(500).json({ error: error.message });
  }
});

// API 路由: 导出 Excel
app.post('/api/export-excel', (req, res) => {
    console.log('收到导出请求');
    try {
      const { headers, data, sheetName } = req.body;
      
      if (!headers || !data) {
        return res.status(400).json({ error: '缺少必要参数' });
      }
      
      // 限制导出数据量
      const MAX_EXPORT_ROWS = 100000;
      const limitedData = data.length > MAX_EXPORT_ROWS ? data.slice(0, MAX_EXPORT_ROWS) : data;
      
      if (data.length > MAX_EXPORT_ROWS) {
        console.log(`警告: 导出数据被限制为 ${MAX_EXPORT_ROWS} 行，原始数据有 ${data.length} 行`);
      }
      
      // 创建工作簿
      const workbook = XLSX.utils.book_new();
      
      // 准备数据（包括表头）
      const wsData = [headers, ...limitedData];
      
      // 创建工作表
      const worksheet = XLSX.utils.aoa_to_sheet(wsData);
      
      // 将工作表添加到工作簿
      XLSX.utils.book_append_sheet(workbook, worksheet, sheetName || 'Sheet1');
      
      // 生成临时文件路径
      const outputPath = path.join(__dirname, 'uploads', `export_${Date.now()}.xlsx`);
      
      // 写入文件
      XLSX.writeFile(workbook, outputPath, { compression: true }); // 启用压缩
      
      // 检查文件是否成功创建
      if (!fs.existsSync(outputPath)) {
        throw new Error('创建Excel文件失败');
      }
      
      // 检查文件大小
      const stats = fs.statSync(outputPath);
      if (stats.size === 0) {
        throw new Error('生成的Excel文件为空');
      }
      
      console.log(`Excel文件已创建: ${outputPath}, 大小: ${stats.size} 字节`);
      
      // 设置响应头，让浏览器下载文件
      res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
      res.setHeader('Content-Disposition', `attachment; filename="${encodeURIComponent(sheetName || 'export')}.xlsx"`);
      res.setHeader('Content-Length', stats.size);
      
      // 创建文件读取流
      const fileStream = fs.createReadStream(outputPath);
      
      // 处理
      fileStream.on('error', (error) => {
        console.error('文件流错误:', error);
        // 如果响应头尚未发送，发送错误响应
        if (!res.headersSent) {
          res.status(500).json({ error: '读取导出文件时出错' });
        } else {
          // 否则结束响应
          res.end();
        }
        
        // 尝试删除临时文件
        try {
          fs.unlinkSync(outputPath);
        } catch (unlinkError) {
          console.error('删除临时文件出错:', unlinkError);
        }
      });
      
      // 管道连接到响应
      fileStream.pipe(res).on('finish', () => {
        console.log('文件传输完成');
        
        // 删除临时文件
        fs.unlink(outputPath, (unlinkErr) => {
          if (unlinkErr) {
            console.error('删除临时文件出错:', unlinkErr);
          }
        });
      });
    } catch (error) {
      console.error('导出Excel出错:', error);
      res.status(500).json({ error: error.message });
      
      // 尝试删除可能创建的临时文件
      const outputPath = path.join(__dirname, 'uploads', `export_${Date.now()}.xlsx`);
      if (fs.existsSync(outputPath)) {
        try {
          fs.unlinkSync(outputPath);
        } catch (unlinkError) {
          console.error('删除临时文件出错:', unlinkError);
        }
      }
    }
  });

// 启动服务器
app.listen(port, () => {
  const localIP = getLocalIP();
  console.log(`服务器已启动:`);
  console.log(`- 本地访问: http://localhost:${port}`);
  console.log(`- 局域网访问: http://${localIP}:${port}`);
});