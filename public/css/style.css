/* public/css/style.css */
body {
    background-color: #f8f9fa;
    font-family: Arial, sans-serif;
  }
  
  .container {
    max-width: 1200px;
  }
  
  .card {
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    border: none;
    margin-bottom: 20px;
  }
  
  .table-responsive {
    max-height: 500px;
    overflow-y: auto;
  }
  
  #dedupResult, #searchResult, #splitResult, #dedupStatus, #searchStatus, #splitStatus {
    font-size: 14px;
    padding: 10px;
    border-radius: 4px;
  }
  
  #dedupStatus, #searchStatus, #splitStatus {
    background-color: #f8f9fa;
    margin-top: 10px;
  }
  
  .btn {
    margin-right: 10px;
  }
  
  /* 添加一些响应式调整 */
  @media (max-width: 768px) {
    .container {
      padding: 10px;
    }
    
    .card-body {
      padding: 15px;
    }
  }