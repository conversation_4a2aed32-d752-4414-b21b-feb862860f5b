<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Excel 电话号码处理工具</title>
  <script src="/js/error-handler.js"></script>
  <!-- 使用本地 Bootstrap CSS -->
  <link rel="stylesheet" href="/css/bootstrap/bootstrap.min.css">
  <link rel="stylesheet" href="/css/style.css">
  <!-- 添加 favicon 链接 -->
  <link rel="icon" href="data:,">
  <!-- 内联样式，确保基本样式即使外部CSS加载失败也能显示 -->
  <style>
    body {
      background-color: #f8f9fa;
      font-family: Arial, sans-serif;
      padding: 20px;
    }
    .container {
      max-width: 1200px;
      margin: 0 auto;
    }
    .card {
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      border: none;
      margin-bottom: 20px;
      background-color: white;
      border-radius: 5px;
    }
    .card-body {
      padding: 20px;
    }
    .btn {
      margin-right: 10px;
    }
    .form-label {
      font-weight: 500;
    }
    .alert {
      padding: 10px;
      border-radius: 4px;
      margin-top: 10px;
    }
    .alert-info {
      background-color: #d1ecf1;
      border-color: #bee5eb;
      color: #0c5460;
    }
    .alert-success {
      background-color: #d4edda;
      border-color: #c3e6cb;
      color: #155724;
    }
    .table {
      width: 100%;
      margin-bottom: 1rem;
      color: #212529;
      border-collapse: collapse;
    }
    .table th, .table td {
      padding: 0.75rem;
      vertical-align: top;
      border-top: 1px solid #dee2e6;
    }
    .table-striped tbody tr:nth-of-type(odd) {
      background-color: rgba(0, 0, 0, 0.05);
    }
  </style>
</head>
<body>
  <div class="container mt-4">
    <h1 class="text-center mb-4">Excel 电话号码处理工具</h1>
    
    <!-- 标签页导航 -->
    <ul class="nav nav-tabs" id="myTab" role="tablist">
      <li class="nav-item" role="presentation">
        <button class="nav-link active" id="dedup-tab" data-bs-toggle="tab" data-bs-target="#dedup" type="button" role="tab" aria-controls="dedup" aria-selected="true">电话号码去重</button>
      </li>
      <li class="nav-item" role="presentation">
        <button class="nav-link" id="search-tab" data-bs-toggle="tab" data-bs-target="#search" type="button" role="tab" aria-controls="search" aria-selected="false">关键词搜索</button>
      </li>
      <li class="nav-item" role="presentation">
        <button class="nav-link" id="split-tab" data-bs-toggle="tab" data-bs-target="#split" type="button" role="tab" aria-controls="split" aria-selected="false">多电话号码分离</button>
      </li>
    </ul>
    
    <!-- 标签页内容 -->
    <div class="tab-content" id="myTabContent">
      <!-- 电话号码去重标签页 -->
      <div class="tab-pane fade show active" id="dedup" role="tabpanel" aria-labelledby="dedup-tab">
        <div class="card mt-3">
          <div class="card-body">
            <!-- <h5 class="card-title">电话号码去重</h5> -->
            
            <form id="dedupForm">
              <div class="mb-3">
                <label for="dedupFile" class="form-label">选择 Excel 文件</label>
                <input type="file" class="form-control" id="dedupFile" accept=".xlsx,.xls" required>
              </div>
              
              <div class="mb-3">
                <label for="dedupSheet" class="form-label">选择工作表</label>
                <select class="form-select" id="dedupSheet" disabled></select>
              </div>
              
              <div class="mb-3">
                <label for="dedupPhoneColumn" class="form-label">选择电话号码列</label>
                <select class="form-select" id="dedupPhoneColumn" disabled></select>
              </div>
              
              <div class="mb-3 form-check">
                <input type="checkbox" class="form-check-input" id="dedupRemoveCountryCode">
                <label class="form-check-label" for="dedupRemoveCountryCode">移除国家代码 (+86/86)</label>
              </div>
              
              <div class="mb-3 form-check">
                <input type="checkbox" class="form-check-input" id="dedupKeepOriginalFormat">
                <label class="form-check-label" for="dedupKeepOriginalFormat">保持原始格式</label>
              </div>
              
              <div class="mb-3 form-check">
                <input type="checkbox" class="form-check-input" id="dedupEnableDateFilter">
                <label class="form-check-label" for="dedupEnableDateFilter">启用日期筛选</label>
              </div>
              
              <div id="dedupDateFilterOptions" style="display: none;">
                <div class="mb-3">
                  <label for="dedupDateColumn" class="form-label">选择日期列</label>
                  <select class="form-select" id="dedupDateColumn"></select>
                </div>
                
                <div class="row">
                  <div class="col-md-6 mb-3">
                    <label for="dedupYear" class="form-label">年份</label>
                    <select class="form-select" id="dedupYear"></select>
                  </div>
                  
                  <div class="col-md-6 mb-3">
                    <label for="dedupMonth" class="form-label">月份</label>
                    <select class="form-select" id="dedupMonth"></select>
                  </div>
                </div>
              </div>
              
              <button type="submit" class="btn btn-primary" id="dedupButton" disabled>去重处理</button>
              <button type="button" class="btn btn-success" id="dedupExportButton" disabled>导出结果</button>
            </form>
            
            <div class="mt-3" id="dedupResult"></div>
            <div class="mt-3" id="dedupStatus"></div>
          </div>
        </div>
      </div>
      
      <!-- 关键词搜索标签页 -->
      <div class="tab-pane fade" id="search" role="tabpanel" aria-labelledby="search-tab">
        <div class="card mt-3">
          <div class="card-body">
            <!-- <h5 class="card-title">关键词搜索</h5> -->
            
            <form id="searchForm">
              <div class="mb-3">
                <label for="searchFile" class="form-label">选择 Excel 文件</label>
                <input type="file" class="form-control" id="searchFile" accept=".xlsx,.xls" required>
              </div>
              
              <div class="mb-3">
                <label for="searchSheet" class="form-label">选择工作表</label>
                <select class="form-select" id="searchSheet" disabled></select>
              </div>
              
              <div class="mb-3">
                <label for="searchColumn" class="form-label">选择搜索列</label>
                <select class="form-select" id="searchColumn" disabled></select>
              </div>
              
              <div class="mb-3">
                <label for="searchKeyword" class="form-label">搜索关键词</label>
                <input type="text" class="form-control" id="searchKeyword" required>
              </div>
              
              <div class="mb-3 form-check">
                <input type="checkbox" class="form-check-input" id="searchCaseSensitive">
                <label class="form-check-label" for="searchCaseSensitive">区分大小写</label>
              </div>
              
              <div class="mb-3 form-check">
                <input type="checkbox" class="form-check-input" id="searchExactMatch">
                <label class="form-check-label" for="searchExactMatch">精确匹配</label>
              </div>
              
              <div class="mb-3 form-check">
                <input type="checkbox" class="form-check-input" id="searchDeduplicateResults">
                <label class="form-check-label" for="searchDeduplicateResults">对结果去重</label>
              </div>
              
              <button type="submit" class="btn btn-primary" id="searchButton" disabled>搜索</button>
              <button type="button" class="btn btn-success" id="searchExportButton" disabled>导出结果</button>
            </form>
            
            <div class="mt-3" id="searchStatus"></div>
            <div class="mt-3" id="searchResult">
              <div id="noSearchResultsMessage" style="display: none;">未找到匹配记录</div>
              <div class="table-responsive">
                <table class="table table-striped" id="searchResultTable" style="display: none;">
                  <tbody id="searchResultBody"></tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 多电话号码分离标签页 -->
      <div class="tab-pane fade" id="split" role="tabpanel" aria-labelledby="split-tab">
        <div class="card mt-3">
          <div class="card-body">
            <!-- <h5 class="card-title">多电话号码分离</h5> -->
            
            <form id="splitForm">
              <div class="mb-3">
                <label for="splitFile" class="form-label">选择 Excel 文件</label>
                <input type="file" class="form-control" id="splitFile" accept=".xlsx,.xls" required>
              </div>
              
              <div class="mb-3">
                <label for="splitSheet" class="form-label">选择工作表</label>
                <select class="form-select" id="splitSheet" disabled></select>
              </div>
              
              <div class="mb-3">
                <label for="splitPhoneColumn" class="form-label">选择电话号码列</label>
                <select class="form-select" id="splitPhoneColumn" disabled></select>
              </div>
              
              <div class="mb-3">
                <label for="splitNameColumn" class="form-label">选择名称列</label>
                <select class="form-select" id="splitNameColumn" disabled></select>
              </div>
              
              <div class="mb-3 form-check">
                <input type="checkbox" class="form-check-input" id="splitRemoveCountryCode">
                <label class="form-check-label" for="splitRemoveCountryCode">移除国家代码 (+86/86)</label>
              </div>
              
              <div class="mb-3 form-check">
                <input type="checkbox" class="form-check-input" id="splitDeduplicateResults">
                <label class="form-check-label" for="splitDeduplicateResults">对结果去重</label>
              </div>
              
              <div class="mb-3 form-check">
                <input type="checkbox" class="form-check-input" id="splitEnableDateFilter">
                <label class="form-check-label" for="splitEnableDateFilter">启用日期筛选</label>
              </div>
              
              <div id="splitDateFilterOptions" style="display: none;">
                <div class="mb-3">
                  <label for="splitDateColumn" class="form-label">选择日期列</label>
                  <select class="form-select" id="splitDateColumn"></select>
                </div>
                
                <div class="row">
                  <div class="col-md-6 mb-3">
                    <label for="splitYear" class="form-label">年份</label>
                    <select class="form-select" id="splitYear"></select>
                  </div>
                  
                  <div class="col-md-6 mb-3">
                    <label for="splitMonth" class="form-label">月份</label>
                    <select class="form-select" id="splitMonth"></select>
                  </div>
                </div>
              </div>
              
              <button type="submit" class="btn btn-primary" id="splitButton" disabled>分离处理</button>
              <button type="button" class="btn btn-success" id="splitExportButton" disabled>导出结果</button>
            </form>
            
            <div class="mt-3" id="splitResult"></div>
            <div class="mt-3" id="splitStatus"></div>
            <div class="mt-3">
              <div id="noSplitResultsMessage" style="display: none;">未找到匹配记录</div>
              <div class="table-responsive">
                <table class="table table-striped" id="splitResultTable" style="display: none;">
                  <tbody id="splitResultBody"></tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  
  <!-- 使用本地 Bootstrap JS -->
  <script src="/js/bootstrap/bootstrap.bundle.min.js"></script>
  <script src="/js/main.js"></script>
</body>
</html>