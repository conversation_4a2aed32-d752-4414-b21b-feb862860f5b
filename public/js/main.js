// 全局变量
let currentFilePath = '';
let currentSheetName = '';
let excelData = null;
let searchResults = null;
let splitResults = null;
let dedupResults = null;

// 防止 MutationObserver 错误
document.addEventListener('DOMContentLoaded', function() {
  // 捕获可能的 MutationObserver 错误
  window.addEventListener('error', function(event) {
    if (event.error && event.error.message && event.error.message.includes('MutationObserver')) {
      console.warn('捕获到 MutationObserver 错误:', event.error.message);
      event.preventDefault();
      return true;
    }
  });
});

// 在 main.js 文件开头添加
// 额外的错误处理
console.log('main.js 加载中...');

// 全局错误处理函数
window.onerror = function(message, source, lineno, colno, error) {
  console.warn('全局错误:', message, 'at', source, lineno, colno);
  // 返回 true 表示错误已处理
  if (message && (message.includes('MutationObserver') || message.includes('observe'))) {
    return true;
  }
  return false;
};

// 其余 main.js 代码保持不变

// DOM 元素
document.addEventListener('DOMContentLoaded', function() {
  // 初始化日期选择器
  initializeDateSelectors();
  
  // 初始化事件监听器
  initializeEventListeners();
});

// 初始化日期选择器
function initializeDateSelectors() {
  const yearSelectors = ['dedupYear', 'splitYear'];
  const monthSelectors = ['dedupMonth', 'splitMonth'];
  
  // 初始化年份选择器
  yearSelectors.forEach(id => {
    const selector = document.getElementById(id);
    if (selector) {
      // 添加空选项
      const emptyOption = document.createElement('option');
      emptyOption.value = '';
      emptyOption.textContent = '-- 选择年份 --';
      selector.appendChild(emptyOption);
      
      // 添加年份选项（当前年份前后5年）
      const currentYear = new Date().getFullYear();
      for (let year = currentYear - 10; year <= currentYear + 15; year++) {
        const option = document.createElement('option');
        option.value = year;
        option.textContent = year;
        selector.appendChild(option);
      }
    }
  });
  
  // 初始化月份选择器
  monthSelectors.forEach(id => {
    const selector = document.getElementById(id);
    if (selector) {
      // 添加空选项
      const emptyOption = document.createElement('option');
      emptyOption.value = '';
      emptyOption.textContent = '-- 选择月份 --';
      selector.appendChild(emptyOption);
      
      // 添加月份选项
      for (let month = 1; month <= 12; month++) {
        const option = document.createElement('option');
        option.value = month;
        option.textContent = month;
        selector.appendChild(option);
      }
    }
  });
  
  // 初始化日期筛选选项显示/隐藏
  const dateFilterCheckboxes = [
    { checkbox: 'dedupEnableDateFilter', options: 'dedupDateFilterOptions' },
    { checkbox: 'splitEnableDateFilter', options: 'splitDateFilterOptions' }
  ];
  
  dateFilterCheckboxes.forEach(item => {
    const checkbox = document.getElementById(item.checkbox);
    const options = document.getElementById(item.options);
    
    if (checkbox && options) {
      checkbox.addEventListener('change', () => {
        options.style.display = checkbox.checked ? 'block' : 'none';
      });
      
      // 默认隐藏日期筛选选项
      options.style.display = 'none';
    }
  });
}

// 初始化事件监听器
function initializeEventListeners() {
  // 去重标签页
  initializeDedupTab();
  
  // 搜索标签页
  initializeSearchTab();
  
  // 分离标签页
  initializeSplitTab();
}

// 初始化去重标签页
function initializeDedupTab() {
  const fileInput = document.getElementById('dedupFile');
  const sheetSelect = document.getElementById('dedupSheet');
  const phoneColumnSelect = document.getElementById('dedupPhoneColumn');
  const dedupButton = document.getElementById('dedupButton');
  const exportButton = document.getElementById('dedupExportButton');
  const dedupForm = document.getElementById('dedupForm');
  
  // 文件上传处理函数 - 去重标签页
  if (fileInput) {
    fileInput.addEventListener('change', async (event) => {
      if (event.target.files.length === 0) return;
      
      const file = event.target.files[0];
      updateStatus('dedupStatus', '正在加载文件...');
      
      try {
        // 创建 FormData 对象
        const formData = new FormData();
        formData.append('file', file);
        
        // 打印一些调试信息
        console.log('上传文件:', file.name, '大小:', file.size, '类型:', file.type);
        
        // 发送请求
        const response = await fetch('/api/get-sheets', {
          method: 'POST',
          body: formData
        });
        
        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || '加载文件失败');
        }
        
        const data = await response.json();
        excelData = data;
        currentFilePath = data.filePath;
        
        // 更新工作表下拉列表
        if (sheetSelect) {
          sheetSelect.innerHTML = '';
          data.sheetNames.forEach(sheetName => {
            const option = document.createElement('option');
            option.value = sheetName;
            option.textContent = sheetName;
            sheetSelect.appendChild(option);
          });
          
          sheetSelect.disabled = false;
          
          // 选择第一个工作表并更新列下拉列表
          if (data.sheetNames.length > 0) {
            currentSheetName = data.sheetNames[0];
            updateColumnSelects('dedup', currentSheetName);
          }
        }
        
        // 启用去重按钮
        if (dedupButton) {
          dedupButton.disabled = false;
        }
        
        updateStatus('dedupStatus', '文件已加载，请选择工作表和列');
      } catch (error) {
        console.error('Error loading file:', error);
        updateStatus('dedupStatus', '加载文件出错: ' + error.message);
      }
    });
  }
  
  if (sheetSelect) {
    sheetSelect.addEventListener('change', () => {
      currentSheetName = sheetSelect.value;
      updateColumnSelects('dedup', currentSheetName);
    });
  }
  
  if (dedupForm) {
    dedupForm.addEventListener('submit', async (event) => {
      event.preventDefault();
      
      if (!excelData || !currentSheetName) {
        updateStatus('dedupStatus', '请先加载Excel文件并选择工作表');
        return;
      }
      
      const phoneColumnIndex = parseInt(phoneColumnSelect.value);
      const removeCountryCode = document.getElementById('dedupRemoveCountryCode').checked;
      const keepOriginalFormat = document.getElementById('dedupKeepOriginalFormat').checked;
      
      // 日期筛选选项
      const enableDateFilter = document.getElementById('dedupEnableDateFilter').checked;
      const dateColumnSelect = document.getElementById('dedupDateColumn');
      const yearSelect = document.getElementById('dedupYear');
      const monthSelect = document.getElementById('dedupMonth');
      
      const dateFilter = {
        enabled: enableDateFilter,
        columnIndex: dateColumnSelect ? parseInt(dateColumnSelect.value) : -1,
        year: yearSelect ? yearSelect.value : '',
        month: monthSelect ? monthSelect.value : ''
      };
      
      updateStatus('dedupStatus', '处理中...');
      
      try {
        const response = await fetch('/api/dedup-phones', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            filePath: currentFilePath,
            sheetName: currentSheetName,
            phoneColumnIndex: phoneColumnIndex,
            removeCountryCode: removeCountryCode,
            keepOriginalFormat: keepOriginalFormat,
            dateFilter: dateFilter
          })
        });
        
        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || '处理失败');
        }
        
        const result = await response.json();
        dedupResults = result;
        
        // 显示结果
        const dedupResultElement = document.getElementById('dedupResult');
        if (dedupResultElement) {
          dedupResultElement.innerHTML = `
            <div class="alert alert-success">
              <p><strong>总处理记录数:</strong> ${result.totalProcessed}</p>
              <p><strong>唯一电话号码数:</strong> ${result.uniqueRows.length}</p>
              <p><strong>重复电话号码数:</strong> ${result.duplicateRows.length}</p>
            </div>
          `;
        }
        
        // 启用导出按钮
        if (exportButton) {
          exportButton.disabled = false;
        }
        
        updateStatus('dedupStatus', '处理完成');
      } catch (error) {
        console.error('Error deduplicating phones:', error);
        updateStatus('dedupStatus', '处理出错: ' + error.message);
      }
    });
  }
  
 // 修改导出按钮点击事件处理函数
if (exportButton) {
    exportButton.addEventListener('click', async () => {
      if (!dedupResults || !dedupResults.uniqueRows || dedupResults.uniqueRows.length === 0) {
        updateStatus('dedupStatus', '没有可导出的结果');
        return;
      }
      
      updateStatus('dedupStatus', '准备导出结果...');
      
      try {
        const response = await fetch('/api/export-excel', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            headers: dedupResults.headers,
            data: dedupResults.uniqueRows,
            sheetName: '去重结果'
          })
        });
        
        // 检查响应状态
        if (!response.ok) {
          // 尝试读取错误信息
          let errorMessage = '导出失败';
          try {
            const contentType = response.headers.get('content-type');
            if (contentType && contentType.includes('application/json')) {
              const errorData = await response.json();
              errorMessage = errorData.error || errorMessage;
            } else {
              // 如果不是 JSON，尝试读取文本
              const errorText = await response.text();
              errorMessage = `导出失败: ${response.status} ${response.statusText}`;
              console.error('服务器返回非JSON响应:', errorText);
            }
          } catch (parseError) {
            console.error('解析错误响应失败:', parseError);
          }
          throw new Error(errorMessage);
        }
        
        // 检查内容类型
        const contentType = response.headers.get('content-type');
        if (!contentType || !contentType.includes('application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')) {
          console.warn('警告: 服务器返回的不是Excel文件。内容类型:', contentType);
        }
        
        // 获取文件名
        const contentDisposition = response.headers.get('content-disposition');
        let filename = '去重结果.xlsx';
        if (contentDisposition) {
          const filenameMatch = contentDisposition.match(/filename="?([^"]*)"?/);
          if (filenameMatch && filenameMatch[1]) {
            filename = filenameMatch[1];
          }
        }
        
        // 创建 Blob 对象
        const blob = await response.blob();
        
        // 检查 blob 大小
        if (blob.size === 0) {
          throw new Error('导出失败: 服务器返回了空文件');
        }
        
        // 创建下载链接
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.style.display = 'none';
        a.href = url;
        a.download = filename;
        
        // 添加到文档并触发点击
        document.body.appendChild(a);
        a.click();
        
        // 清理
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
        
        updateStatus('dedupStatus', '结果已导出');
      } catch (error) {
        console.error('Error exporting results:', error);
        updateStatus('dedupStatus', '导出结果出错: ' + error.message);
      }
    });
  }
}

// 初始化搜索标签页
function initializeSearchTab() {
  const fileInput = document.getElementById('searchFile');
  const sheetSelect = document.getElementById('searchSheet');
  const searchColumnSelect = document.getElementById('searchColumn');
  const searchButton = document.getElementById('searchButton');
  const exportButton = document.getElementById('searchExportButton');
  const searchForm = document.getElementById('searchForm');
  
  // 文件上传处理函数 - 搜索标签页
  if (fileInput) {
    fileInput.addEventListener('change', async (event) => {
      if (event.target.files.length === 0) return;
      
      const file = event.target.files[0];
      updateStatus('searchStatus', '正在加载文件...');
      
      try {
        // 创建 FormData 对象
        const formData = new FormData();
        formData.append('file', file);
        
        // 打印一些调试信息
        console.log('上传文件:', file.name, '大小:', file.size, '类型:', file.type);
        
        // 发送请求
        const response = await fetch('/api/get-sheets', {
          method: 'POST',
          body: formData
        });
        
        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || '加载文件失败');
        }
        
        const data = await response.json();
        excelData = data;
        currentFilePath = data.filePath;
        
        // 更新工作表下拉列表
        if (sheetSelect) {
          sheetSelect.innerHTML = '';
          data.sheetNames.forEach(sheetName => {
            const option = document.createElement('option');
            option.value = sheetName;
            option.textContent = sheetName;
            sheetSelect.appendChild(option);
          });
          
          sheetSelect.disabled = false;
          
          // 选择第一个工作表并更新列下拉列表
          if (data.sheetNames.length > 0) {
            currentSheetName = data.sheetNames[0];
            updateColumnSelects('search', currentSheetName);
          }
        }
        
        // 启用搜索按钮
        if (searchButton) {
          searchButton.disabled = false;
        }
        
        updateStatus('searchStatus', '文件已加载，请选择工作表和列');
      } catch (error) {
        console.error('Error loading file:', error);
        updateStatus('searchStatus', '加载文件出错: ' + error.message);
      }
    });
  }
  
  if (sheetSelect) {
    sheetSelect.addEventListener('change', () => {
      currentSheetName = sheetSelect.value;
      updateColumnSelects('search', currentSheetName);
    });
  }
  
  if (searchForm) {
    searchForm.addEventListener('submit', async (event) => {
      event.preventDefault();
      
      if (!excelData || !currentSheetName) {
        updateStatus('searchStatus', '请先加载Excel文件并选择工作表');
        return;
      }
      
      const searchColumnIndex = parseInt(searchColumnSelect.value);
      const keyword = document.getElementById('searchKeyword').value.trim();
      const caseSensitive = document.getElementById('searchCaseSensitive').checked;
      const exactMatch = document.getElementById('searchExactMatch').checked;
      const deduplicateResults = document.getElementById('searchDeduplicateResults').checked;
      
      if (!keyword) {
        updateStatus('searchStatus', '请输入搜索关键词');
        return;
      }
      
      updateStatus('searchStatus', '搜索中...');
      
      try {
        const response = await fetch('/api/search-excel', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            filePath: currentFilePath,
            sheetName: currentSheetName,
            searchColumnIndex: searchColumnIndex,
            keyword: keyword,
            caseSensitive: caseSensitive,
            exactMatch: exactMatch,
            deduplicateResults: deduplicateResults,
            phoneColumnIndex: parseInt(document.getElementById('dedupPhoneColumn') ? document.getElementById('dedupPhoneColumn').value : 0)
          })
        });
        
        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || '搜索失败');
        }
        
        const result = await response.json();
        searchResults = result;
        
        // 显示结果
        const searchResultTable = document.getElementById('searchResultTable');
        const searchResultBody = document.getElementById('searchResultBody');
        const noResultsMessage = document.getElementById('noSearchResultsMessage');
        
        if (searchResultTable && searchResultBody) {
          // 清空表格
          searchResultBody.innerHTML = '';
          
          if (result.matchedRows.length === 0) {
            // 显示无结果消息
            if (noResultsMessage) {
              noResultsMessage.style.display = 'block';
            }
            searchResultTable.style.display = 'none';
            
            // 禁用导出按钮
            if (exportButton) {
              exportButton.disabled = true;
            }
          } else {
            // 创建表头
            const headerRow = document.createElement('tr');
            result.headers.forEach(header => {
              const th = document.createElement('th');
              th.textContent = header || '';
              headerRow.appendChild(th);
            });
            searchResultBody.appendChild(headerRow);
            
            // 添加数据行
            result.matchedRows.forEach(row => {
              const tr = document.createElement('tr');
              row.forEach(cell => {
                const td = document.createElement('td');
                td.textContent = cell !== null && cell !== undefined ? cell : '';
                tr.appendChild(td);
              });
              searchResultBody.appendChild(tr);
            });
            
            // 显示表格，隐藏无结果消息
            searchResultTable.style.display = 'table';
            if (noResultsMessage) {
              noResultsMessage.style.display = 'none';
            }
            
            // 启用导出按钮
            if (exportButton) {
              exportButton.disabled = false;
            }
          }
        }
        
        updateStatus('searchStatus', `搜索完成，找到 ${result.matchedRows.length} 条匹配记录`);
      } catch (error) {
        console.error('Error searching:', error);
        updateStatus('searchStatus', '搜索出错: ' + error.message);
      }
    });
  }
  
  if (exportButton) {
    exportButton.addEventListener('click', async () => {
      if (!searchResults || !searchResults.matchedRows || searchResults.matchedRows.length === 0) {
        updateStatus('searchStatus', '没有可导出的搜索结果');
        return;
      }
      
      updateStatus('searchStatus', '准备导出搜索结果...');
      
      try {
        const keyword = document.getElementById('searchKeyword').value.trim();
        
        const response = await fetch('/api/export-excel', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            headers: searchResults.headers,
            data: searchResults.matchedRows,
            sheetName: `搜索结果_${keyword}`
          }),
          responseType: 'blob'
        });
        
        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || '导出失败');
        }
        
        // 获取文件名
        const contentDisposition = response.headers.get('content-disposition');
        let filename = `搜索结果_${keyword}.xlsx`;
        if (contentDisposition) {
          const filenameMatch = contentDisposition.match(/filename="?([^"]*)"?/);
          if (filenameMatch && filenameMatch[1]) {
            filename = filenameMatch[1];
          }
        }
        
        // 创建 Blob 对象
        const blob = await response.blob();
        
        // 创建下载链接
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.style.display = 'none';
        a.href = url;
        a.download = filename;
        
        // 添加到文档并触发点击
        document.body.appendChild(a);
        a.click();
        
        // 清理
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
        
        updateStatus('searchStatus', '搜索结果已导出');
      } catch (error) {
        console.error('Error exporting search results:', error);
        updateStatus('searchStatus', '导出搜索结果出错: ' + error.message);
      }
    });
  }
}

// 初始化分离标签页
function initializeSplitTab() {
  const fileInput = document.getElementById('splitFile');
  const sheetSelect = document.getElementById('splitSheet');
  const phoneColumnSelect = document.getElementById('splitPhoneColumn');
  const nameColumnSelect = document.getElementById('splitNameColumn');
  const splitButton = document.getElementById('splitButton');
  const exportButton = document.getElementById('splitExportButton');
  const splitForm = document.getElementById('splitForm');
  
  // 文件上传处理函数 - 分离标签页
  if (fileInput) {
    fileInput.addEventListener('change', async (event) => {
      if (event.target.files.length === 0) return;
      
      const file = event.target.files[0];
      updateStatus('splitStatus', '正在加载文件...');
      
      try {
        // 创建 FormData 对象
        const formData = new FormData();
        formData.append('file', file);
        
        // 打印一些调试信息
        console.log('上传文件:', file.name, '大小:', file.size, '类型:', file.type);
        
        // 发送请求
        const response = await fetch('/api/get-sheets', {
          method: 'POST',
          body: formData
        });
        
        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || '加载文件失败');
        }
        
        const data = await response.json();
        excelData = data;
        currentFilePath = data.filePath;
        
        // 更新工作表下拉列表
        if (sheetSelect) {
          sheetSelect.innerHTML = '';
          data.sheetNames.forEach(sheetName => {
            const option = document.createElement('option');
            option.value = sheetName;
            option.textContent = sheetName;
            sheetSelect.appendChild(option);
          });
          
          sheetSelect.disabled = false;
          
          // 选择第一个工作表并更新列下拉列表
          if (data.sheetNames.length > 0) {
            currentSheetName = data.sheetNames[0];
            updateColumnSelects('split', currentSheetName);
          }
        }
        
        // 启用分离按钮
        if (splitButton) {
          splitButton.disabled = false;
        }
        
        updateStatus('splitStatus', '文件已加载，请选择工作表和列');
      } catch (error) {
        console.error('Error loading file:', error);
        updateStatus('splitStatus', '加载文件出错: ' + error.message);
      }
    });
  }
  
  if (sheetSelect) {
    sheetSelect.addEventListener('change', () => {
      currentSheetName = sheetSelect.value;
      updateColumnSelects('split', currentSheetName);
    });
  }
  
  if (splitForm) {
    splitForm.addEventListener('submit', async (event) => {
      event.preventDefault();
      
      if (!excelData || !currentSheetName) {
        updateStatus('splitStatus', '请先加载Excel文件并选择工作表');
        return;
      }
      
      const phoneColumnIndex = parseInt(phoneColumnSelect.value);
      const nameColumnIndex = parseInt(nameColumnSelect.value);
      const removeCountryCode = document.getElementById('splitRemoveCountryCode').checked;
      const deduplicateResults = document.getElementById('splitDeduplicateResults').checked;
      
      // 日期筛选选项
      const enableDateFilter = document.getElementById('splitEnableDateFilter').checked;
      const dateColumnSelect = document.getElementById('splitDateColumn');
      const yearSelect = document.getElementById('splitYear');
      const monthSelect = document.getElementById('splitMonth');
      
      const dateFilter = {
        enabled: enableDateFilter,
        columnIndex: dateColumnSelect ? parseInt(dateColumnSelect.value) : -1,
        year: yearSelect ? yearSelect.value : '',
        month: monthSelect ? monthSelect.value : ''
      };
      
      updateStatus('splitStatus', '处理中...');
      
      try {
        const response = await fetch('/api/split-phones', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            filePath: currentFilePath,
            sheetName: currentSheetName,
            phoneColumnIndex: phoneColumnIndex,
            nameColumnIndex: nameColumnIndex,
            removeCountryCode: removeCountryCode,
            deduplicateResults: deduplicateResults,
            dateFilter: dateFilter
          })
        });
        
        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || '处理失败');
        }
        
        const result = await response.json();
        splitResults = result;
        
        // 显示结果
        const splitResultElement = document.getElementById('splitResult');
        if (splitResultElement) {
          splitResultElement.innerHTML = `
            <div class="alert alert-success">
              <p><strong>总处理记录数:</strong> ${result.totalProcessed}</p>
              <p><strong>分离后的记录数:</strong> ${result.splitRows.length}</p>
            </div>
          `;
        }
        
        // 显示结果表格
        const splitResultTable = document.getElementById('splitResultTable');
        const splitResultBody = document.getElementById('splitResultBody');
        const noResultsMessage = document.getElementById('noSplitResultsMessage');
        
        if (splitResultTable && splitResultBody) {
          // 清空表格
          splitResultBody.innerHTML = '';
          
          if (result.splitRows.length === 0) {
            // 显示无结果消息
            if (noResultsMessage) {
              noResultsMessage.style.display = 'block';
            }
            splitResultTable.style.display = 'none';
            
            // 禁用导出按钮
            if (exportButton) {
              exportButton.disabled = true;
            }
          } else {
            // 创建表头
            const headerRow = document.createElement('tr');
            result.headers.forEach(header => {
              const th = document.createElement('th');
              th.textContent = header || '';
              headerRow.appendChild(th);
            });
            splitResultBody.appendChild(headerRow);
            
            // 添加数据行
            result.splitRows.forEach(row => {
              const tr = document.createElement('tr');
              row.forEach(cell => {
                const td = document.createElement('td');
                td.textContent = cell !== null && cell !== undefined ? cell : '';
                tr.appendChild(td);
              });
              splitResultBody.appendChild(tr);
            });
            
            // 显示表格，隐藏无结果消息
            splitResultTable.style.display = 'table';
            if (noResultsMessage) {
              noResultsMessage.style.display = 'none';
            }
            
            // 启用导出按钮
            if (exportButton) {
              exportButton.disabled = false;
            }
          }
        }
        
        updateStatus('splitStatus', `处理完成，分离出 ${result.splitRows.length} 条记录`);
      } catch (error) {
        console.error('Error splitting phones:', error);
        updateStatus('splitStatus', '处理出错: ' + error.message);
      }
    });
  }
  
  if (exportButton) {
    exportButton.addEventListener('click', async () => {
      if (!splitResults || !splitResults.splitRows || splitResults.splitRows.length === 0) {
        updateStatus('splitStatus', '没有可导出的分离结果');
        return;
      }
      
      updateStatus('splitStatus', '准备导出分离结果...');
      
      try {
        const response = await fetch('/api/export-excel', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            headers: splitResults.headers,
            data: splitResults.splitRows,
            sheetName: '分离结果'
          }),
          responseType: 'blob'
        });
        
        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || '导出失败');
        }
        
        // 获取文件名
        const contentDisposition = response.headers.get('content-disposition');
        let filename = '分离结果.xlsx';
        if (contentDisposition) {
          const filenameMatch = contentDisposition.match(/filename="?([^"]*)"?/);
          if (filenameMatch && filenameMatch[1]) {
            filename = filenameMatch[1];
          }
        }
        
        // 创建 Blob 对象
        const blob = await response.blob();
        
        // 创建下载链接
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.style.display = 'none';
        a.href = url;
        a.download = filename;
        
        // 添加到文档并触发点击
        document.body.appendChild(a);
        a.click();
        
        // 清理
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
        
        updateStatus('splitStatus', '分离结果已导出');
      } catch (error) {
        console.error('Error exporting split results:', error);
        updateStatus('splitStatus', '导出分离结果出错: ' + error.message);
      }
    });
}
}
function updateColumnSelects(tabPrefix, sheetName) {
    if (!excelData || !excelData.sheets || !excelData.sheets[sheetName]) {
      console.error('No sheet data available for', sheetName);
      return;
    }
  
    const headers = excelData.sheets[sheetName].headers;
    
    // 根据标签页前缀获取相应的选择器
    const selectors = {
      dedup: ['dedupPhoneColumn', 'dedupDateColumn'],
      search: ['searchColumn'],
      split: ['splitPhoneColumn', 'splitNameColumn', 'splitDateColumn']
    };
    
    // 更新选择器
    (selectors[tabPrefix] || []).forEach(selectorId => {
      const select = document.getElementById(selectorId);
      if (select) {
        select.innerHTML = '';
        headers.forEach((header, index) => {
          const option = document.createElement('option');
          option.value = index;
          option.textContent = header || `列 ${index + 1}`;
          select.appendChild(option);
        });
        
        select.disabled = false;
        
        // 自动选择合适的列
        if (selectorId.includes('Phone')) {
          // 尝试找到电话号码列
          for (let i = 0; i < headers.length; i++) {
            const header = String(headers[i] || '').toLowerCase();
            if (header.includes('电话') || header.includes('手机') || header.includes('联系方式') || header.includes('phone')) {
              select.value = i;
              break;
            }
          }
        } else if (selectorId.includes('Name')) {
          // 尝试找到名称列
          for (let i = 0; i < headers.length; i++) {
            const header = String(headers[i] || '').toLowerCase();
            if (header.includes('名称') || header.includes('公司') || header.includes('企业') || header.includes('name') || header.includes('company')) {
              select.value = i;
              break;
            }
          }
        } else if (selectorId.includes('Date')) {
          // 尝试找到日期列
          for (let i = 0; i < headers.length; i++) {
            const header = String(headers[i] || '').toLowerCase();
            if (header.includes('日期') || header.includes('时间') || header.includes('date') || header.includes('time')) {
              select.value = i;
              break;
            }
          }
        }
      }
    });
  }
  
  // 更新状态信息
  function updateStatus(elementId, message) {
    const statusElement = document.getElementById(elementId);
    if (statusElement) {
      statusElement.innerHTML = `<div class="alert alert-info">${message}</div>`;
    }
    console.log(`${elementId}: ${message}`);
  }