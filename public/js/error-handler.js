// 在页面加载前处理 MutationObserver 错误
(function() {
    // 保存原始的 MutationObserver
    const originalMutationObserver = window.MutationObserver;
    
    // 创建一个安全的 MutationObserver 包装器
    function SafeMutationObserver(callback) {
      // 如果不是函数，直接返回原始构造函数的实例
      if (typeof callback !== 'function') {
        console.warn('MutationObserver 需要一个函数作为参数');
        return new originalMutationObserver(function() {});
      }
      
      // 创建一个安全的回调函数
      const safeCallback = function(mutations, observer) {
        try {
          return callback(mutations, observer);
        } catch (error) {
          console.warn('MutationObserver 回调错误被捕获:', error);
          return null;
        }
      };
      
      // 返回原始 MutationObserver 的实例，但使用安全回调
      return new originalMutationObserver(safeCallback);
    }
    
    // 复制原型和静态属性
    SafeMutationObserver.prototype = originalMutationObserver.prototype;
    Object.setPrototypeOf(SafeMutationObserver, originalMutationObserver);
    
    // 替换全局 MutationObserver
    window.MutationObserver = SafeMutationObserver;
    
    // 捕获全局错误
    window.addEventListener('error', function(event) {
      if (event.error && event.error.message && 
          (event.error.message.includes('MutationObserver') || 
           event.error.message.includes('observe'))) {
        console.warn('全局 MutationObserver 错误被捕获:', event.error.message);
        event.preventDefault();
        return true;
      }
    }, true);
    
    // 捕获未处理的 Promise 拒绝
    window.addEventListener('unhandledrejection', function(event) {
      if (event.reason && event.reason.message && 
          (event.reason.message.includes('MutationObserver') || 
           event.reason.message.includes('observe'))) {
        console.warn('未处理的 Promise 拒绝 (MutationObserver):', event.reason.message);
        event.preventDefault();
        return true;
      }
    });
    // 在 error-handler.js 中添加
// 完全禁用 MutationObserver
window.MutationObserver = function() {
    return {
      observe: function() { console.log('MutationObserver.observe 已被禁用'); },
      disconnect: function() { console.log('MutationObserver.disconnect 已被禁用'); },
      takeRecords: function() { console.log('MutationObserver.takeRecords 已被禁用'); return []; }
    };
  };
    
    console.log('MutationObserver 错误处理器已安装');
  })();